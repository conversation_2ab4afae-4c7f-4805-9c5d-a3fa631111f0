"""
Response Utilities Service

This service provides shared response handling functionality including:
- Response data preparation
- Standardized response formatting
- Debug and extended mode handling
- Response logging utilities
"""

import logging
from typing import Dict, Any
from agritram.message_utils import StandardSuccessResponse

logger = logging.getLogger(__name__)


class ResponseUtilsService:
    """Shared response utilities for user services"""

    @classmethod
    def create_success_response(
        cls,
        code: str,
        message: str,
        details: str = None,
        actions: str = None,
        data: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Create a standardized success response

        Args:
            code: Response code
            message: Response message
            details: Response details
            actions: Suggested actions
            data: Response data

        Returns:
            Standardized success response
        """
        return StandardSuccessResponse.create_success_response(
            code=code, message=message, details=details, actions=actions, data=data
        )

    @classmethod
    def prepare_user_data(cls, user, include_sensitive: bool = False) -> Dict[str, Any]:
        """
        Prepare user data for response

        Args:
            user: User instance
            include_sensitive: Whether to include sensitive data

        Returns:
            Dict containing user data
        """
        user_data = {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "role": user.role,
            "is_mail_verified": user.is_mail_verified,
            "date_joined": user.date_joined.isoformat() if user.date_joined else None,
        }

        if include_sensitive:
            user_data.update(
                {
                    "account_address": user.account_address,
                    "opt_in": user.opt_in,
                    "is_active": user.is_active,
                }
            )

        return user_data

    @classmethod
    def prepare_device_data(
        cls,
        device_id: str,
        device_registered: bool = False,
        additional_info: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Prepare device data for response

        Args:
            device_id: Device identifier
            device_registered: Whether device was registered
            additional_info: Additional device information

        Returns:
            Dict containing device data
        """
        device_data = {
            "id": device_id,
            "registered": device_registered,
        }

        if additional_info:
            device_data.update(additional_info)

        return device_data

    @classmethod
    def prepare_registration_response_data(
        cls,
        user,
        device_result: Dict[str, Any],
        activation_result: Dict[str, Any],
        debug_mode: bool = False,
        extended_mode: bool = False,
    ) -> Dict[str, Any]:
        """
        Prepare registration response data

        Args:
            user: User instance
            device_result: Device registration result
            activation_result: Activation flow result
            debug_mode: Include debug information
            extended_mode: Include extended information

        Returns:
            Dict containing response data
        """
        response_data = {
            "user": cls.prepare_user_data(user, include_sensitive=extended_mode),
            "device": cls.prepare_device_data(
                device_result.get("device_id"),
                device_result.get("device_registered", False),
            ),
            "activation_required": not user.is_mail_verified,
            "email_sent": activation_result.get("email_sent", False),
        }

        if debug_mode:
            response_data["debug"] = {
                "device_registration_details": device_result,
                "activation_details": activation_result,
                "user_status": {
                    "is_active": user.is_active,
                    "is_mail_verified": user.is_mail_verified,
                },
            }

        if extended_mode:
            response_data["extended"] = {
                "registration_timestamp": (
                    user.date_joined.isoformat() if user.date_joined else None
                ),
                "activation_url": activation_result.get("activation_url"),
                "token_generated": bool(activation_result.get("activation_token")),
            }

        return response_data

    @classmethod
    def prepare_activation_response_data(
        cls,
        device_id: str,
        device_registered: bool = False,
        welcome_email_sent: bool = False,
        additional_data: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Prepare activation response data

        Args:
            device_id: Device identifier
            device_registered: Whether device was registered
            welcome_email_sent: Whether welcome email was sent
            additional_data: Additional data to include

        Returns:
            Dict containing response data
        """
        response_data = {
            "device": cls.prepare_device_data(device_id, device_registered),
            "welcome_email_sent": welcome_email_sent,
        }

        if additional_data:
            response_data.update(additional_data)

        return response_data

    @classmethod
    def create_registration_success_response(
        cls,
        user,
        device_result: Dict[str, Any],
        activation_result: Dict[str, Any],
        debug_mode: bool = False,
        extended_mode: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a complete registration success response

        Args:
            user: User instance
            device_result: Device registration result
            activation_result: Activation flow result
            debug_mode: Include debug information
            extended_mode: Include extended information

        Returns:
            Complete registration response
        """
        response_data = cls.prepare_registration_response_data(
            user, device_result, activation_result, debug_mode, extended_mode
        )

        return cls.create_success_response(
            code=StandardSuccessResponse.USER_CREATED,
            message="Registration successful",
            details="Your account has been created successfully. Please check your email to activate your account.",
            actions="Check your email and click the activation link to complete your registration",
            data=response_data,
        )

    @classmethod
    def create_activation_success_response(
        cls,
        device_id: str,
        device_registered: bool = False,
        welcome_email_sent: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a complete activation success response

        Args:
            device_id: Device identifier
            device_registered: Whether device was registered
            welcome_email_sent: Whether welcome email was sent

        Returns:
            Complete activation response
        """
        response_data = cls.prepare_activation_response_data(
            device_id, device_registered, welcome_email_sent
        )

        return cls.create_success_response(
            code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
            message="Account activated successfully",
            details="Your account has been activated and is now ready to use",
            actions="You can now log in to access all features",
            data=response_data,
        )

    @classmethod
    def create_already_activated_response(cls) -> Dict[str, Any]:
        """
        Create response for already activated account

        Returns:
            Already activated response
        """
        return cls.create_success_response(
            code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
            message="Account is already activated",
            details="Your account was previously activated and is ready to use",
            actions="You can now log in to your account",
        )

    @classmethod
    def log_response_creation(
        cls,
        response_type: str,
        message: str,
        user_id: int = None,
        request_id: str = None,
        additional_data: Dict[str, Any] = None,
    ) -> None:
        """
        Log response creation with standardized format

        Args:
            response_type: Type of response (SUCCESS, ERROR, etc.)
            message: Response message
            user_id: User ID (optional)
            request_id: Request ID (optional)
            additional_data: Additional data to log
        """
        extra_data = {
            "operation": "RESPONSE_CREATION",
            "response_type": response_type,
            "response_message": message,
        }

        if user_id:
            extra_data["user_id"] = user_id

        if request_id:
            extra_data["request_id"] = request_id

        if additional_data:
            extra_data.update(additional_data)

        logger.info(f"Response created: {response_type} - {message}", extra=extra_data)

    @classmethod
    def sanitize_response_data(
        cls, data: Dict[str, Any], remove_fields: list = None
    ) -> Dict[str, Any]:
        """
        Sanitize response data by removing sensitive fields

        Args:
            data: Response data to sanitize
            remove_fields: List of fields to remove

        Returns:
            Sanitized response data
        """
        if remove_fields is None:
            remove_fields = ["password", "token", "activation_token"]

        sanitized_data = data.copy()

        for field in remove_fields:
            if field in sanitized_data:
                del sanitized_data[field]

        # Recursively sanitize nested dictionaries
        for key, value in sanitized_data.items():
            if isinstance(value, dict):
                sanitized_data[key] = cls.sanitize_response_data(value, remove_fields)

        return sanitized_data
