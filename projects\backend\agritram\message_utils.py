import logging
from django.conf import settings
from rest_framework.response import Response
from rest_framework import status
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


def get_frontend_url_by_role(role):
    """
    Get the appropriate frontend URL based on user role
    """
    role_urls = {
        "farmer": settings.FARMER_FRONTEND_URL,
        "trader": settings.TRADER_FRONTEND_URL,
        "manufacturer": settings.MANUFACTURER_FRONTEND_URL,
    }

    return role_urls.get(role, settings.FARMER_FRONTEND_URL)  # Default to farmer URL


def detect_user_role_from_request(request):
    """
    Detect user role based on the request origin (Referer header or Origin header)
    """
    # Get the origin or referer from the request
    origin = request.META.get("HTTP_ORIGIN") or request.META.get("HTTP_REFERER", "")

    # Check against configured frontend URLs
    if (
        hasattr(settings, "FARMER_FRONTEND_URL")
        and settings.FARMER_FRONTEND_URL in origin
    ):
        return "farmer"
    elif (
        hasattr(settings, "TRADER_FRONTEND_URL")
        and settings.TRADER_FRONTEND_URL in origin
    ):
        return "trader"
    elif (
        hasattr(settings, "MANUFACTURER_FRONTEND_URL")
        and settings.MANUFACTURER_FRONTEND_URL in origin
    ):
        return "manufacturer"

    # Default to farmer if no match found
    return "farmer"


class StandardErrorResponse:
    """
    Standardized error response utility for consistent API error formatting.

    Format:
    {
        "error": {
            "code": "ERROR_CODE",
            "message": "Something went wrong",
            "details": "Detailed explanation (optional)",
            "actions": "Try refreshing token or contact support." // optional
        }
    }
    """

    # Standard error codes
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    NOT_FOUND_ERROR = "NOT_FOUND_ERROR"
    BUSINESS_LOGIC_ERROR = "BUSINESS_LOGIC_ERROR"
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR"
    SERVER_ERROR = "SERVER_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    DEVICE_VERIFICATION_REQUIRED = "DEVICE_VERIFICATION_REQUIRED"
    INVALID_TOKEN = "INVALID_TOKEN"
    EXPIRED_TOKEN = "EXPIRED_TOKEN"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    DUPLICATE_RESOURCE = "DUPLICATE_RESOURCE"
    INVALID_REQUEST = "INVALID_REQUEST"

    @staticmethod
    def create_error_response(
        code: str,
        message: str,
        details: Optional[str] = None,
        actions: Optional[str] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> Response:
        """
        Create a standardized error response.

        Args:
            code: Error code (use predefined constants)
            message: Human-readable error message
            details: Optional detailed explanation
            actions: Optional suggested actions for the user
            status_code: HTTP status code
            extra_data: Optional additional data to include in response

        Returns:
            DRF Response object with standardized error format
        """
        error_data = {"code": code, "message": message}

        if details:
            error_data["details"] = details

        if actions:
            error_data["actions"] = actions

        response_data = {"error": error_data}

        # Add any extra data at the root level
        if extra_data:
            response_data.update(extra_data)

        return Response(response_data, status=status_code)

    @staticmethod
    def validation_error(
        message: str = "Validation failed",
        details: Optional[str] = None,
        field_errors: Optional[Dict[str, Any]] = None,
    ) -> Response:
        """Create a validation error response."""
        extra_data = {}
        if field_errors:
            extra_data["field_errors"] = field_errors

        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.VALIDATION_ERROR,
            message=message,
            details=details,
            actions="Please check your input and try again.",
            status_code=status.HTTP_400_BAD_REQUEST,
            extra_data=extra_data,
        )

    @staticmethod
    def authentication_error(
        message: str = "Authentication failed", details: Optional[str] = None
    ) -> Response:
        """Create an authentication error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.AUTHENTICATION_ERROR,
            message=message,
            details=details,
            actions="Please log in and try again.",
            status_code=status.HTTP_401_UNAUTHORIZED,
        )

    @staticmethod
    def authorization_error(
        message: str = "Access denied", details: Optional[str] = None
    ) -> Response:
        """Create an authorization error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.AUTHORIZATION_ERROR,
            message=message,
            details=details,
            actions="Contact support if you believe this is an error.",
            status_code=status.HTTP_403_FORBIDDEN,
        )

    @staticmethod
    def not_found_error(
        message: str = "Resource not found", details: Optional[str] = None
    ) -> Response:
        """Create a not found error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.NOT_FOUND_ERROR,
            message=message,
            details=details,
            status_code=status.HTTP_404_NOT_FOUND,
        )

    @staticmethod
    def server_error(
        message: str = "Internal server error", details: Optional[str] = None
    ) -> Response:
        """Create a server error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.SERVER_ERROR,
            message=message,
            details=details,
            actions="Please try again later or contact support.",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

    @staticmethod
    def rate_limit_error(
        message: str = "Rate limit exceeded",
        details: Optional[str] = None,
        retry_after: Optional[int] = None,
    ) -> Response:
        """Create a rate limit error response."""
        extra_data = {}
        if retry_after:
            extra_data["retry_after"] = retry_after

        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.RATE_LIMIT_ERROR,
            message=message,
            details=details,
            actions=(
                f"Please wait {retry_after} seconds before trying again."
                if retry_after
                else "Please wait before trying again."
            ),
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            extra_data=extra_data,
        )

    @staticmethod
    def device_verification_required(
        message: str = "Device verification required",
        details: Optional[str] = None,
        device_id: Optional[str] = None,
    ) -> Response:
        """Create a device verification required response."""
        extra_data = {"verification_required": True}
        if device_id:
            extra_data["device_id"] = device_id

        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.DEVICE_VERIFICATION_REQUIRED,
            message=message,
            details=details,
            actions="Please check your email for verification code.",
            status_code=status.HTTP_202_ACCEPTED,
            extra_data=extra_data,
        )

    @staticmethod
    def business_logic_error(
        message: str = "Business logic error", details: Optional[str] = None
    ) -> Response:
        """Create a business logic error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.BUSINESS_LOGIC_ERROR,
            message=message,
            details=details,
            actions="Please check your request and try again.",
            status_code=status.HTTP_400_BAD_REQUEST,
        )

    @staticmethod
    def duplicate_resource_error(
        message: str = "Resource already exists", details: Optional[str] = None
    ) -> Response:
        """Create a duplicate resource error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.DUPLICATE_RESOURCE,
            message=message,
            details=details,
            actions="Use a different identifier or update the existing resource.",
            status_code=status.HTTP_409_CONFLICT,
        )

    @staticmethod
    def invalid_token_error(
        message: str = "Invalid token", details: Optional[str] = None
    ) -> Response:
        """Create an invalid token error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.INVALID_TOKEN,
            message=message,
            details=details,
            actions="Please refresh your token or log in again.",
            status_code=status.HTTP_401_UNAUTHORIZED,
        )

    @staticmethod
    def expired_token_error(
        message: str = "Token has expired", details: Optional[str] = None
    ) -> Response:
        """Create an expired token error response."""
        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.EXPIRED_TOKEN,
            message=message,
            details=details,
            actions="Please refresh your token or log in again.",
            status_code=status.HTTP_401_UNAUTHORIZED,
        )

    @staticmethod
    def external_service_error(
        message: str = "External service error",
        details: Optional[str] = None,
        service_name: Optional[str] = None,
    ) -> Response:
        """Create an external service error response."""
        extra_data = {}
        if service_name:
            extra_data["service"] = service_name

        return StandardErrorResponse.create_error_response(
            code=StandardErrorResponse.EXTERNAL_SERVICE_ERROR,
            message=message,
            details=details,
            actions="Please try again later or contact support.",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            extra_data=extra_data,
        )


class StandardSuccessResponse:
    """
    Standardized success response utility for consistent API success formatting.

    Format:
    {
        "success": {
            "code": "SUCCESS_CODE",
            "message": "Operation completed successfully",
            "details": "Detailed explanation (optional)",
            "actions": "Next steps or recommendations (optional)"
        },
        "data": {...} // optional additional data
    }
    """

    # Standard success codes
    LOGIN_SUCCESS = "LOGIN_SUCCESS"
    LOGOUT_SUCCESS = "LOGOUT_SUCCESS"
    USER_CREATED = "USER_CREATED"
    ACCOUNT_ACTIVATED = "ACCOUNT_ACTIVATED"
    PASSWORD_RESET_SUCCESS = "PASSWORD_RESET_SUCCESS"
    PASSWORD_CHANGED = "PASSWORD_CHANGED"
    PROFILE_UPDATED = "PROFILE_UPDATED"
    TOKEN_REFRESHED = "TOKEN_REFRESHED"

    RECORD_CREATED = "RECORD_CREATED"
    RECORD_UPDATED = "RECORD_UPDATED"
    RECORD_DELETED = "RECORD_DELETED"
    DATA_RETRIEVED = "DATA_RETRIEVED"

    TRANSACTION_COMPLETED = "TRANSACTION_COMPLETED"
    TRANSFER_SUCCESS = "TRANSFER_SUCCESS"
    PAYMENT_SUCCESS = "PAYMENT_SUCCESS"

    CROP_CREATED = "CROP_CREATED"
    CROP_UPDATED = "CROP_UPDATED"
    CROP_TRANSFER_SUCCESS = "CROP_TRANSFER_SUCCESS"

    INVENTORY_UPDATED = "INVENTORY_UPDATED"
    INVENTORY_RETRIEVED = "INVENTORY_RETRIEVED"

    PROOF_OF_UNLOCK_CREATED = "PROOF_OF_UNLOCK_CREATED"
    PROOF_OF_UNLOCK_UPDATED = "PROOF_OF_UNLOCK_UPDATED"

    FILE_UPLOADED = "FILE_UPLOADED"
    EMAIL_SENT = "EMAIL_SENT"
    VALIDATION_PASSED = "VALIDATION_PASSED"
    OPERATION_SUCCESS = "OPERATION_SUCCESS"
    REQUEST_PROCESSED = "REQUEST_PROCESSED"

    @staticmethod
    def create_success_response(
        code: str,
        message: str,
        details: Optional[str] = None,
        actions: Optional[str] = None,
        status_code: int = status.HTTP_200_OK,
        data: Optional[Dict[str, Any]] = None,
    ) -> Response:
        """
        Create a standardized success response.

        Args:
            code: Success code (use predefined constants)
            message: Human-readable success message
            details: Optional detailed explanation
            actions: Optional suggested next steps for the user
            status_code: HTTP status code
            data: Optional additional data to include in response

        Returns:
            DRF Response object with standardized success format
        """
        success_data = {"code": code, "message": message}

        if details:
            success_data["details"] = details

        if actions:
            success_data["actions"] = actions

        response_data = {"success": success_data}

        # Add any additional data
        if data:
            response_data["data"] = data

        return Response(response_data, status=status_code)

    @staticmethod
    def login_success(
        message: str = "User logged in successfully",
        details: Optional[str] = None,
        user_data: Optional[Dict[str, Any]] = None,
        token_data: Optional[Dict[str, Any]] = None,
    ) -> Response:
        """Create a login success response."""
        data = {}
        if user_data:
            data["user"] = user_data
        if token_data:
            data.update(token_data)

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.LOGIN_SUCCESS,
            message=message,
            details=details
            or "Authentication token generated and user session established",
            actions="You can now access protected resources",
            data=data if data else None,
        )

    @staticmethod
    def registration_success(
        message: str = "User registered successfully",
        details: Optional[str] = None,
        user_data: Optional[Dict[str, Any]] = None,
    ) -> Response:
        """Create a registration success response."""
        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.USER_CREATED,
            message=message,
            details=details or "Account created and activation email sent",
            actions="Please check your email to activate your account",
            status_code=status.HTTP_201_CREATED,
            data=user_data if user_data and len(user_data) > 0 else None,
        )

    @staticmethod
    def record_created(
        message: str = "Record created successfully",
        details: Optional[str] = None,
        record_data: Optional[Dict[str, Any]] = None,
        record_id: Optional[str] = None,
    ) -> Response:
        """Create a record creation success response."""
        if record_id and not details:
            details = f"New record added with ID: {record_id}"

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.RECORD_CREATED,
            message=message,
            details=details,
            actions="View the record or create another one",
            status_code=status.HTTP_201_CREATED,
            data=record_data,
        )

    @staticmethod
    def record_updated(
        message: str = "Record updated successfully",
        details: Optional[str] = None,
        record_data: Optional[Dict[str, Any]] = None,
    ) -> Response:
        """Create a record update success response."""
        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.RECORD_UPDATED,
            message=message,
            details=details or "Changes have been saved successfully",
            actions="Changes are now live across all systems",
            data=record_data,
        )

    @staticmethod
    def data_retrieved(
        message: str = "Data retrieved successfully",
        details: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        count: Optional[int] = None,
    ) -> Response:
        """Create a data retrieval success response."""
        if count is not None and not details:
            details = f"Retrieved {count} record{'s' if count != 1 else ''}"

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.DATA_RETRIEVED,
            message=message,
            details=details,
            data=data,
        )

    @staticmethod
    def transaction_completed(
        message: str = "Transaction processed successfully",
        details: Optional[str] = None,
        transaction_data: Optional[Dict[str, Any]] = None,
        amount: Optional[str] = None,
    ) -> Response:
        """Create a transaction completion success response."""
        if amount and not details:
            details = f"Transaction of {amount} confirmed and recorded"

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.TRANSACTION_COMPLETED,
            message=message,
            details=details,
            actions="Transaction details have been saved to your history",
            data=transaction_data,
        )


# Convenience functions for common error scenarios
def handle_serializer_errors(serializer) -> Response:
    """
    Convert DRF serializer errors to standardized format.

    Args:
        serializer: DRF serializer with validation errors

    Returns:
        Standardized error response
    """
    field_errors = {}
    non_field_errors = []

    for field, errors in serializer.errors.items():
        if field == "non_field_errors":
            non_field_errors.extend(errors)
        else:
            field_errors[field] = errors

    # Create main error message
    if non_field_errors:
        message = non_field_errors[0]
        details = "; ".join(non_field_errors) if len(non_field_errors) > 1 else None
    elif field_errors:
        # Use first field error as main message
        first_field = next(iter(field_errors))
        first_error = field_errors[first_field]
        message = f"{first_field}: {first_error[0] if isinstance(first_error, list) else first_error}"
        details = "Multiple validation errors occurred"
    else:
        message = "Validation failed"
        details = None

    return StandardErrorResponse.validation_error(
        message=message,
        details=details,
        field_errors=field_errors if field_errors else None,
    )


def handle_registration_serializer_errors(serializer) -> Response:
    """
    Convert DRF serializer errors to standardized format for registration.
    This version excludes field_errors from the response as per requirements.

    Args:
        serializer: DRF serializer with validation errors

    Returns:
        Standardized error response without field_errors
    """
    field_errors = {}
    non_field_errors = []

    for field, errors in serializer.errors.items():
        if field == "non_field_errors":
            non_field_errors.extend(errors)
        else:
            field_errors[field] = errors

    # Create main error message
    if non_field_errors:
        message = non_field_errors[0]
        details = "; ".join(non_field_errors) if len(non_field_errors) > 1 else None
    elif field_errors:
        # Use first field error as main message
        first_field = next(iter(field_errors))
        first_error = field_errors[first_field]
        message = f"{first_field}: {first_error[0] if isinstance(first_error, list) else first_error}"
        details = "Multiple validation errors occurred"
    else:
        message = "Validation failed"
        details = None

    # Use create_error_response directly to avoid including field_errors
    return StandardErrorResponse.create_error_response(
        code=StandardErrorResponse.VALIDATION_ERROR,
        message=message,
        details=details,
        actions="Please check your input and try again.",
        status_code=status.HTTP_400_BAD_REQUEST,
    )


def handle_exception_with_logging(
    exception: Exception,
    context: str = "",
    request=None,
    user=None,
) -> Response:
    """
    Handle unexpected exceptions with standardized logging and return standardized error.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        exception: The exception that occurred
        context: Additional context for logging
        request: Django request object (optional)
        user: User object (optional)

    Returns:
        Standardized server error response
    """

    # Prepare error message
    error_message = (
        f"Unexpected error in {context}: {str(exception)}"
        if context
        else f"Unexpected error: {str(exception)}"
    )

    # Log with standardized logging
    logger.error(f"[EXCEPTION_HANDLING] {error_message}")

    # Don't expose internal error details in production
    if settings.DEBUG:
        details = str(exception)
    else:
        details = "An unexpected error occurred"

    return StandardErrorResponse.server_error(
        message="Internal server error", details=details
    )


def handle_serializer_errors_with_logging(
    serializer,
    request=None,
    user=None,
    context: str = "serializer_validation",
) -> Response:
    """
    Convert DRF serializer errors to standardized format with logging.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        serializer: DRF serializer with validation errors
        request: Django request object (optional)
        user: User object (optional)
        context: Additional context for logging

    Returns:
        Standardized error response
    """
    # Process serializer errors
    field_errors = {}
    non_field_errors = []

    for field, errors in serializer.errors.items():
        if field == "non_field_errors":
            non_field_errors.extend(errors)
        else:
            field_errors[field] = errors

    # Create main error message
    if non_field_errors:
        message = non_field_errors[0]
        details = "; ".join(non_field_errors) if len(non_field_errors) > 1 else None
    elif field_errors:
        # Use first field error as main message
        first_field = next(iter(field_errors))
        first_error = field_errors[first_field]
        message = f"{first_field}: {first_error[0] if isinstance(first_error, list) else first_error}"
        details = "Multiple validation errors occurred"
    else:
        message = "Validation failed"
        details = None

    # Log validation errors with standardized logging
    logger.warning(
        f"[SERIALIZER_VALIDATION_ERROR] Serializer validation failed in {context}"
    )

    return StandardErrorResponse.validation_error(
        message=message,
        details=details,
        field_errors=field_errors if field_errors else None,
    )


def log_response_creation(
    response_type: str,
    message: str,
    status_code: int,
    request=None,
    user=None,
    additional_data: Optional[Dict[str, Any]] = None,
):
    """
    Log the creation of standardized responses.
    Uses thread-local storage to automatically get the unique ID.

    Args:
        response_type: Type of response (SUCCESS, ERROR, etc.)
        message: Response message
        status_code: HTTP status code
        request: Django request object (optional)
        user: User object (optional)
        additional_data: Additional data to include in logs
    """

    # Prepare metadata
    metadata = {
        "response_type": response_type,
        "response_message": message,
        "status_code": status_code,
    }

    if additional_data:
        metadata.update(additional_data)

    # Determine log level based on status code
    if status_code >= 500:
        level = "ERROR"
    elif status_code >= 400:
        level = "WARNING"
    else:
        level = "INFO"

    # Log response creation
    logger.info(
        f"[RESPONSE_{response_type}] Created {response_type.lower()} response: {message}"
    )
