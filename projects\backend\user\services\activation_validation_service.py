"""
Activation Validation Service

This service handles all validation logic for user activation including:
- Parameter validation
- Token validation
- Device validation
- User lookup and verification
"""

import logging
from typing import Dict, Any, Optional, Tuple

from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode
from oauth2_auth.utils import get_client_ip, get_dynamic_device_info
from oauth2_auth.secure_token_service import secure_token_service
from oauth2_auth.device_validation_service import device_validation_service
from agritram.exceptions import ValidationException
from user.models import User

logger = logging.getLogger(__name__)


class ActivationValidationService:
    """Service for handling all activation validation logic"""

    @staticmethod
    def _extract_activation_parameters(
        request, uid: Optional[str], token: Optional[str]
    ) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Extract activation parameters from request"""
        if request.method == "GET":
            # Handle URL parameters (from email links)
            return uid, token, request.GET.get("device_id")
        else:
            # Handle POST request body
            try:
                return (
                    request.data.get("uid") or uid,
                    request.data.get("token") or token,
                    request.data.get("device_id"),
                )
            except Exception:
                return None, None, None

    @staticmethod
    def _handle_device_id_from_header(
        request, device_id: Optional[str], request_id: str
    ) -> Optional[str]:
        """Handle device ID extraction from X-Device-ID header"""
        header_device_id = request.META.get("HTTP_X_DEVICE_ID", "").strip()
        if header_device_id:
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_DEVICE_ID_FROM_HEADER | message=Device ID extracted from X-Device-ID header | request_id={request_id} | device_id={header_device_id}"
            )
            return header_device_id
        elif not device_id:
            # Generate device ID if not provided
            from .device_management_service import DeviceManagementService

            generated_device_id = DeviceManagementService.generate_device_id()
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_DEVICE_ID_GENERATED | message=Generated device ID for activation | request_id={request_id} | device_id={generated_device_id}"
            )
            return generated_device_id

        return device_id

    @staticmethod
    def _raise_validation_error(message: str, details: str) -> None:
        """Raise a validation error with consistent format"""
        raise ValidationException(message=message, details=details)

    def validate_activation_request(
        self, request, uid: Optional[str], token: Optional[str], request_id: str
    ) -> Dict[str, Any]:
        """
        Validate complete activation request and return validated data

        Args:
            request: HTTP request object
            uid: User ID from URL (optional)
            token: Activation token from URL (optional)
            request_id: Unique request ID for logging

        Returns:
            Dict containing validated activation data

        Raises:
            ValidationException: If validation fails
        """
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        # Extract activation parameters
        uidb64, validated_token, device_id = self._extract_activation_parameters(
            request, uid, token
        )

        # Handle device ID from X-Device-ID header or generate if missing
        device_id = self._handle_device_id_from_header(request, device_id, request_id)

        # Get dynamic device information
        device_info = get_dynamic_device_info(request, "activation")
        device_name = device_info["device_name"]
        device_type = device_info["device_type"]

        # Log parameter extraction
        logger.info(
            f"OPERATION_INFO: ACTIVATION_PARAMS_EXTRACTED | message=Activation parameters extracted | request_id={request_id} | method={request.method} | has_uid={bool(uidb64)} | has_token={bool(validated_token)} | has_device_id={bool(device_id)} | device_name={device_name} | device_type={device_type}"
        )

        # Validate required parameters
        if not uidb64 or not validated_token:
            missing_params = []
            if not uidb64:
                missing_params.append("uid")
            if not validated_token:
                missing_params.append("token")

            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_MISSING_PARAMS | message=Activation attempt with missing parameters | request_id={request_id} | missing_params={missing_params} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            self._raise_validation_error(
                message="Invalid activation link",
                details="Both UID and token are required for account activation",
            )

        # Validate and extract user
        try:
            uid_decoded = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid_decoded)

            # Log successful user lookup
            logger.debug(
                f"OPERATION_DEBUG: ACTIVATION_USER_FOUND | message=User found for activation | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'}"
            )

        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_INVALID_UID | message=Activation attempt with invalid UID | request_id={request_id} | uid={uidb64} | error_type={type(e).__name__} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            self._raise_validation_error(
                message="Invalid activation link",
                details="The activation link is malformed or the user does not exist",
            )

        # Validate activation token
        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_TOKEN_VALIDATION_START | message=Starting token validation | request_id={request_id} | user_id={user.id}"
        )

        is_valid, token_obj, error_message = secure_token_service.validate_token(
            raw_token=validated_token,
            token_type="activation",
            request=request,
            user=user,
        )

        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_TOKEN_VALIDATION_RESULT | message=Token validation completed | request_id={request_id} | user_id={user.id} | is_valid={is_valid} | error_message={error_message if not is_valid else 'None'}"
        )

        if not is_valid or not token_obj:
            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_INVALID_TOKEN | message=Activation attempt with invalid or expired token | request_id={request_id} | user_id={user.id} | email={user.email[:3]}***@{user.email.split('@')[1] if '@' in user.email else '***'} | error_message={error_message} | token_status={token_obj.status if token_obj else 'not_found'} | ip={client_ip} | user_agent={user_agent[:100]}"
            )
            self._raise_validation_error(
                message="Activation link is invalid or has expired",
                details=error_message,
            )

        # Validate device consistency for activation
        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_DEVICE_VALIDATION_START | message=Starting device validation | request_id={request_id} | user_id={user.id} | device_id={device_id}"
        )

        is_device_valid, device_message, device_details = (
            device_validation_service.validate_activation_device(
                user=user, device_id=device_id, request=request
            )
        )

        logger.debug(
            f"OPERATION_DEBUG: ACTIVATION_DEVICE_VALIDATION_RESULT | message=Device validation completed | request_id={request_id} | user_id={user.id} | is_device_valid={is_device_valid} | security_score={device_details.get('security_score', 'N/A')} | device_message={device_message}"
        )

        # Log device validation result (but don't block activation unless security score is very low)
        if not is_device_valid and device_details.get("security_score", 100) < 30:
            import json

            logger.warning(
                f"OPERATION_WARNING: ACTIVATION_DEVICE_VALIDATION_FAILED | message=Device validation failed during activation - blocking activation | request_id={request_id} | user_id={user.id} | device_id={device_id} | security_score={device_details.get('security_score')} | ip={client_ip} | user_agent={user_agent[:100]} | device_details={json.dumps(device_details, separators=(',', ':'))}"
            )
            self._raise_validation_error(
                message="Device validation failed",
                details="The device used for activation doesn't match the registration device",
            )

        return {
            "user": user,
            "uidb64": uidb64,
            "token": validated_token,
            "token_obj": token_obj,
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
            "device_validation": {
                "is_valid": is_device_valid,
                "message": device_message,
                "details": device_details,
            },
        }
