import json

# Test the JSON serialization fix
def make_json_serializable(obj):
    """Convert objects to JSON serializable format"""
    if isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [make_json_serializable(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        return str(obj)  # Convert objects to string representation
    elif isinstance(obj, (str, int, float, bool)) or obj is None:
        return obj
    else:
        return str(obj)  # Fallback to string representation

# Mock problematic object
class MockLimitedStream:
    def __init__(self, data):
        self.data = data
    def __str__(self):
        return f"<MockLimitedStream: {self.data}>"

# Test data that would cause the original error
test_data = {
    'uid': 'test-uid',
    'token': 'secret-token',
    'problematic_object': MockLimitedStream('some data')
}

print("Testing JSON serialization fix...")

try:
    # This would fail with the original code
    safe_data = make_json_serializable(test_data)
    result = json.dumps(safe_data, separators=(',', ':'))
    print(f"✅ SUCCESS: {result}")
except Exception as e:
    print(f"❌ FAILED: {e}")

print("JSON serialization fix test completed!")
