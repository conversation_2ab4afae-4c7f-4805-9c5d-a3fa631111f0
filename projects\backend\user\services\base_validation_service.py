"""
Base Validation Service

This service provides shared validation functionality including:
- Common validation patterns
- Error handling utilities
- Device validation helpers
- User lookup and verification
"""

import logging
from typing import Dict, Any
from agritram.exceptions import ValidationException
from user.models import User

logger = logging.getLogger(__name__)


class BaseValidationService:
    """Base validation service with common validation patterns"""

    @staticmethod
    def raise_validation_error(message: str, details: str = None) -> None:
        """
        Raise a validation error with consistent format

        Args:
            message: Error message
            details: Error details
        """
        raise ValidationException(message=message, details=details or message)

    @classmethod
    def validate_required_fields(
        cls, data: Dict[str, Any], required_fields: list, context: str = "request"
    ) -> None:
        """
        Validate that required fields are present and not empty

        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            context: Context for error messages

        Raises:
            ValidationException: If required fields are missing
        """
        missing_fields = []

        for field in required_fields:
            value = data.get(field)
            if not value or (isinstance(value, str) and not value.strip()):
                missing_fields.append(field)

        if missing_fields:
            cls.raise_validation_error(
                message=f"Missing required fields in {context}",
                details=f"The following fields are required: {', '.join(missing_fields)}",
            )

    @classmethod
    def validate_email_format(cls, email: str) -> None:
        """
        Validate email format

        Args:
            email: Email address to validate

        Raises:
            ValidationException: If email format is invalid
        """
        if not email:
            cls.raise_validation_error(
                message="Email is required",
                details="Email address cannot be empty",
            )

        # Basic email validation
        if "@" not in email or "." not in email.split("@")[-1]:
            cls.raise_validation_error(
                message="Invalid email format",
                details="Please provide a valid email address",
            )

        # Check for common issues
        if email.count("@") != 1:
            cls.raise_validation_error(
                message="Invalid email format",
                details="Email must contain exactly one @ symbol",
            )

    @classmethod
    def validate_device_id_format(cls, device_id: str) -> None:
        """
        Validate device ID format

        Args:
            device_id: Device ID to validate

        Raises:
            ValidationException: If device ID format is invalid
        """
        if not device_id:
            cls.raise_validation_error(
                message="Device ID is required",
                details="Device ID cannot be empty",
            )

        # Check minimum length for security
        if len(device_id) < 10:
            cls.raise_validation_error(
                message="Invalid device ID format",
                details="Device ID must be at least 10 characters long",
            )

        # Check maximum length to prevent abuse
        if len(device_id) > 100:
            cls.raise_validation_error(
                message="Invalid device ID format",
                details="Device ID cannot exceed 100 characters",
            )

    @classmethod
    def validate_user_exists(cls, user_id: int) -> User:
        """
        Validate that a user exists and return the user instance

        Args:
            user_id: User ID to validate

        Returns:
            User instance

        Raises:
            ValidationException: If user does not exist
        """
        try:
            user = User.objects.get(pk=user_id)
            return user
        except User.DoesNotExist:
            cls.raise_validation_error(
                message="User not found",
                details=f"No user found with ID {user_id}",
            )

    @classmethod
    def validate_user_by_email(cls, email: str) -> User:
        """
        Validate that a user exists by email and return the user instance

        Args:
            email: Email address to validate

        Returns:
            User instance

        Raises:
            ValidationException: If user does not exist
        """
        try:
            user = User.objects.get(email=email)
            return user
        except User.DoesNotExist:
            cls.raise_validation_error(
                message="User not found",
                details=f"No user found with email {email}",
            )

    @classmethod
    def validate_user_status(
        cls, user: User, required_status: Dict[str, bool] = None
    ) -> None:
        """
        Validate user status against requirements

        Args:
            user: User instance to validate
            required_status: Dict of status requirements (e.g., {'is_active': True})

        Raises:
            ValidationException: If user status doesn't meet requirements
        """
        if not required_status:
            return

        for status_field, required_value in required_status.items():
            if not hasattr(user, status_field):
                continue

            actual_value = getattr(user, status_field)
            if actual_value != required_value:
                status_name = status_field.replace("_", " ").title()
                cls.raise_validation_error(
                    message=f"User {status_name} validation failed",
                    details=f"User {status_name} is {actual_value}, but {required_value} is required",
                )

    @classmethod
    def validate_string_length(
        cls, value: str, field_name: str, min_length: int = None, max_length: int = None
    ) -> None:
        """
        Validate string length constraints

        Args:
            value: String value to validate
            field_name: Name of the field for error messages
            min_length: Minimum length requirement
            max_length: Maximum length requirement

        Raises:
            ValidationException: If length constraints are not met
        """
        if not value:
            if min_length and min_length > 0:
                cls.raise_validation_error(
                    message=f"{field_name} is required",
                    details=f"{field_name} cannot be empty",
                )
            return

        if min_length and len(value) < min_length:
            cls.raise_validation_error(
                message=f"{field_name} is too short",
                details=f"{field_name} must be at least {min_length} characters long",
            )

        if max_length and len(value) > max_length:
            cls.raise_validation_error(
                message=f"{field_name} is too long",
                details=f"{field_name} cannot exceed {max_length} characters",
            )

    @classmethod
    def validate_choice_field(
        cls, value: str, field_name: str, valid_choices: list
    ) -> None:
        """
        Validate that a value is in the list of valid choices

        Args:
            value: Value to validate
            field_name: Name of the field for error messages
            valid_choices: List of valid choices

        Raises:
            ValidationException: If value is not in valid choices
        """
        if value not in valid_choices:
            cls.raise_validation_error(
                message=f"Invalid {field_name}",
                details=f"{field_name} must be one of: {', '.join(valid_choices)}",
            )

    @classmethod
    def validate_boolean_field(
        cls, value: Any, field_name: str, required: bool = False
    ) -> bool:
        """
        Validate and convert boolean field

        Args:
            value: Value to validate and convert
            field_name: Name of the field for error messages
            required: Whether the field is required

        Returns:
            Boolean value

        Raises:
            ValidationException: If validation fails
        """
        if value is None or value == "":
            if required:
                cls.raise_validation_error(
                    message=f"{field_name} is required",
                    details=f"{field_name} cannot be empty",
                )
            return False

        if isinstance(value, bool):
            return value

        if isinstance(value, str):
            if value.lower() in ("true", "1", "yes", "on"):
                return True
            elif value.lower() in ("false", "0", "no", "off"):
                return False

        cls.raise_validation_error(
            message=f"Invalid {field_name} value",
            details=f"{field_name} must be a boolean value",
        )

    @classmethod
    def log_validation_result(
        cls,
        validation_type: str,
        success: bool,
        details: Dict[str, Any] = None,
        request_id: str = None,
    ) -> None:
        """
        Log validation results with standardized format

        Args:
            validation_type: Type of validation performed
            success: Whether validation succeeded
            details: Additional details to log
            request_id: Request ID for correlation
        """
        status = "SUCCESS" if success else "FAILED"
        extra_data = {
            "operation": f"VALIDATION_{status}",
            "validation_type": validation_type,
        }

        if request_id:
            extra_data["request_id"] = request_id

        if details:
            extra_data.update(details)

        log_level = logger.info if success else logger.warning
        log_level(f"Validation {status.lower()}: {validation_type}", extra=extra_data)
