"""
Tests for the activation account functionality with improved logging and error handling.
"""

from unittest.mock import Mock, patch
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from rest_framework.test import APIClient
from rest_framework import status

from user.views import activate_account
from oauth2_auth.models import SecureToken

User = get_user_model()


class ActivationAccountTestCase(TestCase):
    """Test cases for account activation functionality"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.client = APIClient()

        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>",
            name="Test User",
            password="TestPassword123!",
            role="farmer",
            is_mail_verified=False,
        )

        # Create UID for activation
        self.uid = urlsafe_base64_encode(force_bytes(self.user.pk))
        self.token = "test-activation-token"

    def tearDown(self):
        """Clean up test data"""
        User.objects.filter(email="<EMAIL>").delete()
        SecureToken.objects.all().delete()

    @patch(
        "user.services.activation_orchestrator.ActivationOrchestrator.activate_user_account"
    )
    def test_successful_activation_get_request(self, mock_orchestrator):
        """Test successful account activation via GET request"""
        # Arrange
        from agritram.message_utils import StandardSuccessResponse

        mock_orchestrator.return_value = (
            StandardSuccessResponse.create_success_response(
                code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                message="Account activated successfully",
                details="Your account has been activated and is now ready to use",
                actions="You can now log in to access all features",
                data={"device": {"id": "test-device-123"}},
            )
        )

        # Act
        response = self.client.get(f"/user/activate-account/{self.uid}/{self.token}/")

        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("success", response.data)
        self.assertEqual(
            response.data["success"]["message"], "Account activated successfully"
        )

        # Verify orchestrator was called with correct parameters
        mock_orchestrator.assert_called_once()
        call_args = mock_orchestrator.call_args
        self.assertEqual(call_args[0][1], self.uid)  # uid parameter
        self.assertEqual(call_args[0][2], self.token)  # token parameter

    @patch(
        "user.services.activation_orchestrator.ActivationOrchestrator.activate_user_account"
    )
    def test_successful_activation_post_request(self, mock_orchestrator):
        """Test successful account activation via POST request"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "mobile",
        }
        mock_token_service.validate_token.return_value = (True, Mock(id=1), None)
        mock_device_validation.validate_activation_device.return_value = (
            True,
            "Device valid",
            {"security_score": 90},
        )

        request_data = {
            "uid": self.uid,
            "token": self.token,
            "device_id": "test-device-123",
        }

        # Act
        response = self.client.post(
            "/user/activate-account/", request_data, format="json"
        )

        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertTrue(self.user.is_mail_verified)

    def test_activation_missing_parameters(self):
        """Test activation with missing parameters"""
        # Act - Missing token
        response = self.client.get(f"/user/activate-account/{self.uid}/")

        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    def test_activation_invalid_uid(self):
        """Test activation with invalid UID"""
        # Act
        response = self.client.get(f"/user/activate-account/invalid-uid/{self.token}/")

        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_activation_already_active_user(self):
        """Test activation attempt for already active user"""
        # Arrange - Activate user first
        self.user.is_mail_verified = True
        self.user.save()

        # Act
        response = self.client.get(f"/user/activate-account/{self.uid}/{self.token}/")

        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.data["success"]["message"], "Account is already activated"
        )

    @patch("user.views.secure_token_service")
    @patch("oauth2_auth.utils.get_dynamic_device_info")
    def test_activation_invalid_token(self, mock_device_info, mock_token_service):
        """Test activation with invalid token"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web",
        }
        mock_token_service.validate_token.return_value = (False, None, "Token expired")

        # Act
        response = self.client.get(f"/user/activate-account/{self.uid}/{self.token}/")

        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    @patch("user.views.secure_token_service")
    @patch("user.views.device_validation_service")
    @patch("oauth2_auth.utils.get_dynamic_device_info")
    def test_activation_device_validation_failure(
        self, mock_device_info, mock_device_validation, mock_token_service
    ):
        """Test activation with device validation failure"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web",
        }
        mock_token_service.validate_token.return_value = (True, Mock(id=1), None)
        mock_device_validation.validate_activation_device.return_value = (
            False,
            "Device mismatch",
            {"security_score": 25},  # Low security score
        )

        # Act
        response = self.client.get(f"/user/activate-account/{self.uid}/{self.token}/")

        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)

    @patch("user.views.logger")
    @patch("user.views.secure_token_service")
    @patch("oauth2_auth.utils.get_dynamic_device_info")
    def test_activation_logging_patterns(
        self, mock_device_info, mock_token_service, mock_logger
    ):
        """Test that activation uses proper logging patterns"""
        # Arrange
        mock_device_info.return_value = {
            "device_name": "Test Device",
            "device_type": "web",
        }
        mock_token_service.validate_token.return_value = (True, Mock(id=1), None)

        # Act
        response = self.client.get(f"/user/activate-account/{self.uid}/{self.token}/")

        # Assert - Check that logging was called with proper format
        self.assertTrue(mock_logger.info.called)

        # Verify logging calls contain expected patterns
        log_calls = [call[0][0] for call in mock_logger.info.call_args_list]

        # Check for key logging events
        activation_start_logged = any(
            "ACTIVATION_REQUEST_START" in call for call in log_calls
        )
        params_extracted_logged = any(
            "ACTIVATION_PARAMS_EXTRACTED" in call for call in log_calls
        )
        user_found_logged = any("ACTIVATION_USER_FOUND" in call for call in log_calls)

        self.assertTrue(activation_start_logged, "Activation start should be logged")
        self.assertTrue(
            params_extracted_logged, "Parameter extraction should be logged"
        )
        self.assertTrue(user_found_logged, "User found should be logged")

    def test_activation_response_format(self):
        """Test that activation response follows standard format"""
        # Arrange - Already active user for simple test
        self.user.is_mail_verified = True
        self.user.save()

        # Act
        response = self.client.get(f"/user/activate-account/{self.uid}/{self.token}/")

        # Assert - Check response structure
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("success", response.data)
        self.assertIn("code", response.data["success"])
        self.assertIn("message", response.data["success"])
        self.assertIn("details", response.data["success"])
        self.assertIn("actions", response.data["success"])


class ActivationLoggingTestCase(TestCase):
    """Test cases specifically for activation logging functionality"""

    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()

        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>",
            name="Logging Test User",
            password="TestPassword123!",
            role="trader",
            is_mail_verified=False,
        )

        self.uid = urlsafe_base64_encode(force_bytes(self.user.pk))

    def tearDown(self):
        """Clean up test data"""
        User.objects.filter(email="<EMAIL>").delete()

    @patch("user.views.logger")
    def test_email_masking_in_logs(self, mock_logger):
        """Test that email addresses are properly masked in logs"""
        # Arrange
        request = self.factory.get(f"/user/activate-account/{self.uid}/test-token/")

        # Act
        try:
            activate_account(request, uid=self.uid, token="test-token")
        except Exception:
            pass  # We expect this to fail, we're just testing logging

        # Assert - Check that email is masked in logs
        log_calls = [call[0][0] for call in mock_logger.info.call_args_list]

        # Find log entries that should contain masked email
        user_found_logs = [
            call for call in log_calls if "ACTIVATION_USER_FOUND" in call
        ]

        if user_found_logs:
            # Check that email is masked (should contain *** but not full email)
            log_entry = user_found_logs[0]
            self.assertIn("***", log_entry)
            self.assertNotIn("<EMAIL>", log_entry)

    @patch("user.views.logger")
    def test_single_line_json_logging(self, mock_logger):
        """Test that request data is logged in single-line JSON format"""
        # Arrange
        request_data = {
            "uid": self.uid,
            "token": "test-token",
            "device_id": "test-device",
        }
        request = self.factory.post("/user/activate-account/", request_data)

        # Act
        try:
            activate_account(request)
        except Exception:
            pass  # We expect this to fail, we're just testing logging

        # Assert - Check for JSON format in logs
        log_calls = [call[0][0] for call in mock_logger.info.call_args_list]

        # Find the initial request log
        request_start_logs = [
            call for call in log_calls if "ACTIVATION_REQUEST_START" in call
        ]

        if request_start_logs:
            log_entry = request_start_logs[0]
            # Should contain JSON data
            self.assertIn("data=", log_entry)
            # Should be single line (no newlines in JSON)
            json_part = log_entry.split("data=")[1] if "data=" in log_entry else ""
            self.assertNotIn("\n", json_part)
