"""
Tests for the ActivationOrchestrator service

This module contains tests for the refactored activation flow using the orchestrator pattern.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from rest_framework.test import APITestCase
from rest_framework import status

from user.services.activation_orchestrator import ActivationOrchestrator
from agritram.exceptions import ValidationException
from agritram.message_utils import StandardSuccessResponse

User = get_user_model()


class TestActivationOrchestrator(TestCase):
    """Test cases for the ActivationOrchestrator service"""

    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.orchestrator = ActivationOrchestrator()
        
        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='TestPassword123!',
            role='farmer',
            is_mail_verified=False
        )
        
        # Create UID for activation
        self.uid = urlsafe_base64_encode(force_bytes(self.user.pk))
        self.token = 'test-activation-token'

    def tearDown(self):
        """Clean up test data"""
        User.objects.filter(email='<EMAIL>').delete()

    @patch('user.services.activation_orchestrator.ActivationValidationService')
    @patch('user.services.activation_orchestrator.ActivationService')
    def test_successful_activation_flow(self, mock_activation_service, mock_validation_service):
        """Test successful activation flow through orchestrator"""
        # Arrange
        request = self.factory.get(f'/user/activate-account/{self.uid}/{self.token}/')
        
        # Mock validation service
        mock_validation_service.return_value.validate_activation_request.return_value = {
            'user': self.user,
            'uidb64': self.uid,
            'token': self.token,
            'token_obj': Mock(id=1),
            'device_id': 'test-device-123',
            'device_name': 'Test Device',
            'device_type': 'web',
            'device_validation': {
                'is_valid': True,
                'message': 'Device valid',
                'details': {'security_score': 85}
            }
        }
        
        # Mock activation service
        mock_activation_service.return_value.activate_user_account.return_value = {
            'user_activated': True,
            'device_registered': True,
            'welcome_email_sent': True,
            'device_id': 'test-device-123'
        }

        # Act
        result = self.orchestrator.activate_user_account(request, self.uid, self.token)

        # Assert
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
        self.assertEqual(result['success']['message'], 'Account activated successfully')
        self.assertEqual(result['success']['data']['device']['id'], 'test-device-123')
        
        # Verify services were called
        mock_validation_service.return_value.validate_activation_request.assert_called_once()
        mock_activation_service.return_value.activate_user_account.assert_called_once()

    @patch('user.services.activation_orchestrator.ActivationValidationService')
    def test_already_active_user(self, mock_validation_service):
        """Test activation attempt for already active user"""
        # Arrange
        self.user.is_mail_verified = True
        self.user.save()
        
        request = self.factory.get(f'/user/activate-account/{self.uid}/{self.token}/')
        
        # Mock validation service
        mock_validation_service.return_value.validate_activation_request.return_value = {
            'user': self.user,
            'uidb64': self.uid,
            'token': self.token,
            'token_obj': Mock(id=1),
            'device_id': 'test-device-123',
            'device_name': 'Test Device',
            'device_type': 'web',
            'device_validation': {
                'is_valid': True,
                'message': 'Device valid',
                'details': {'security_score': 85}
            }
        }

        # Act
        result = self.orchestrator.activate_user_account(request, self.uid, self.token)

        # Assert
        self.assertIsInstance(result, dict)
        self.assertIn('success', result)
        self.assertEqual(result['success']['message'], 'Account is already activated')

    @patch('user.services.activation_orchestrator.ActivationValidationService')
    def test_validation_exception_propagation(self, mock_validation_service):
        """Test that validation exceptions are properly propagated"""
        # Arrange
        request = self.factory.get(f'/user/activate-account/{self.uid}/{self.token}/')
        
        # Mock validation service to raise exception
        mock_validation_service.return_value.validate_activation_request.side_effect = ValidationException(
            message="Invalid activation link",
            details="Token has expired"
        )

        # Act & Assert
        with self.assertRaises(ValidationException) as context:
            self.orchestrator.activate_user_account(request, self.uid, self.token)
        
        self.assertEqual(str(context.exception), "Invalid activation link")

    def test_logging_data_masking(self):
        """Test that sensitive data is properly masked in logs"""
        # Arrange
        request_data = {
            'uid': self.uid,
            'token': 'sensitive-token-123',
            'device_id': 'device-123'
        }
        headers = {
            'HTTP_USER_AGENT': 'Test Browser',
            'HTTP_X_DEVICE_ID': 'very-long-device-id-********'
        }

        # Act
        log_data = self.orchestrator._create_activation_log_data(request_data, headers, '<EMAIL>')

        # Assert
        import json
        parsed_data = json.loads(log_data)
        
        # Check that sensitive data is masked
        self.assertEqual(parsed_data['request_data']['token'], '***MASKED***')
        self.assertEqual(parsed_data['request_data']['email'], 'tes***@example.com')
        self.assertEqual(parsed_data['headers']['HTTP_X_DEVICE_ID'], 'very***5678')


class TestActivationOrchestratorIntegration(APITestCase):
    """Integration test cases for the complete activation flow"""

    def setUp(self):
        """Set up test fixtures"""
        # Create test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Integration Test User',
            password='TestPassword123!',
            role='trader',
            is_mail_verified=False
        )
        
        # Create UID for activation
        self.uid = urlsafe_base64_encode(force_bytes(self.user.pk))
        self.token = 'integration-test-token'

    def tearDown(self):
        """Clean up test data"""
        User.objects.filter(email='<EMAIL>').delete()

    @patch('user.services.activation_validation_service.secure_token_service')
    @patch('user.services.activation_validation_service.device_validation_service')
    @patch('user.services.activation_service.DeviceAuthenticationService')
    @patch('user.services.activation_service.async_email_service')
    @patch('oauth2_auth.utils.get_dynamic_device_info')
    def test_complete_activation_flow_integration(self, mock_device_info, mock_email_service, 
                                                mock_device_auth, mock_device_validation, 
                                                mock_token_service):
        """Test complete activation flow integration"""
        # Arrange
        mock_device_info.return_value = {
            'device_name': 'Integration Test Device',
            'device_type': 'web'
        }
        mock_token_service.validate_token.return_value = (True, Mock(id=1, status='active'), None)
        mock_device_validation.validate_activation_device.return_value = (
            True, 'Device valid', {'security_score': 90}
        )
        mock_device_auth.register_device.return_value = None
        mock_email_service.send_welcome_email_async.return_value = Mock(id='task-123')

        # Act
        response = self.client.get(f'/user/activate-account/{self.uid}/{self.token}/')

        # Assert
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('success', response.data)
        self.assertEqual(response.data['success']['message'], 'Account activated successfully')
        
        # Verify user was activated in database
        self.user.refresh_from_db()
        self.assertTrue(self.user.is_mail_verified)

    def test_activation_missing_parameters_integration(self):
        """Test activation with missing parameters through complete flow"""
        # Act - Missing token
        response = self.client.get(f'/user/activate-account/{self.uid}/')

        # Assert
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
