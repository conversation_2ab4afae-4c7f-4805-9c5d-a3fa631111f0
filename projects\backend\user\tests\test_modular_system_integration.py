"""
Integration tests for the refactored modular system

This module tests the integration between the new shared utility services
and the refactored orchestrators to ensure no regressions were introduced.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model

User = get_user_model()


class TestModularSystemIntegration(TestCase):
    """Test the integration of the refactored modular system"""

    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            name='Test User',
            password='TestPassword123!',
            role='farmer',
            is_mail_verified=False
        )

    def tearDown(self):
        """Clean up test data"""
        User.objects.filter(email='<EMAIL>').delete()

    def test_logging_utils_service_structure(self):
        """Test LoggingUtilsService has required methods"""
        from user.services.logging_utils_service import LoggingUtilsService
        
        # Test class methods exist
        self.assertTrue(hasattr(LoggingUtilsService, 'mask_email'))
        self.assertTrue(hasattr(LoggingUtilsService, 'mask_device_id'))
        self.assertTrue(hasattr(LoggingUtilsService, 'create_single_line_json'))
        self.assertTrue(hasattr(LoggingUtilsService, 'log_operation_start'))
        self.assertTrue(hasattr(LoggingUtilsService, 'log_operation_step'))

    def test_request_utils_service_structure(self):
        """Test RequestUtilsService has required methods"""
        from user.services.request_utils_service import RequestUtilsService
        
        # Test class methods exist
        self.assertTrue(hasattr(RequestUtilsService, 'extract_request_data'))
        self.assertTrue(hasattr(RequestUtilsService, 'get_request_context'))
        self.assertTrue(hasattr(RequestUtilsService, 'extract_device_id_from_request'))
        self.assertTrue(hasattr(RequestUtilsService, 'handle_device_id_extraction'))

    def test_response_utils_service_structure(self):
        """Test ResponseUtilsService has required methods"""
        from user.services.response_utils_service import ResponseUtilsService
        
        # Test class methods exist
        self.assertTrue(hasattr(ResponseUtilsService, 'create_success_response'))
        self.assertTrue(hasattr(ResponseUtilsService, 'prepare_user_data'))
        self.assertTrue(hasattr(ResponseUtilsService, 'create_registration_success_response'))
        self.assertTrue(hasattr(ResponseUtilsService, 'create_activation_success_response'))

    def test_base_validation_service_structure(self):
        """Test BaseValidationService has required methods"""
        from user.services.base_validation_service import BaseValidationService
        
        # Test class methods exist
        self.assertTrue(hasattr(BaseValidationService, 'validate_email_format'))
        self.assertTrue(hasattr(BaseValidationService, 'validate_required_fields'))
        self.assertTrue(hasattr(BaseValidationService, 'raise_validation_error'))
        self.assertTrue(hasattr(BaseValidationService, 'validate_user_exists'))

    def test_device_management_service_enhanced_structure(self):
        """Test DeviceManagementService has enhanced methods"""
        from user.services.device_management_service import DeviceManagementService
        
        # Test class constants exist
        self.assertTrue(hasattr(DeviceManagementService, 'CONTEXT_REGISTRATION'))
        self.assertTrue(hasattr(DeviceManagementService, 'CONTEXT_ACTIVATION'))
        self.assertTrue(hasattr(DeviceManagementService, 'CONTEXT_AUTHENTICATION'))
        
        # Test enhanced methods exist
        self.assertTrue(hasattr(DeviceManagementService, 'register_device'))
        self.assertTrue(hasattr(DeviceManagementService, 'track_device'))
        self.assertTrue(hasattr(DeviceManagementService, 'handle_device_flow'))
        self.assertTrue(hasattr(DeviceManagementService, 'handle_activation_device_flow'))
        
        # Test legacy methods still exist for backward compatibility
        self.assertTrue(hasattr(DeviceManagementService, 'track_registration_device'))
        self.assertTrue(hasattr(DeviceManagementService, 'handle_device_registration_flow'))

    def test_base_orchestrator_inheritance(self):
        """Test that orchestrators properly inherit from BaseOrchestrator"""
        from user.services.base_orchestrator import BaseOrchestrator
        from user.services.activation_orchestrator import ActivationOrchestrator
        from user.services.registration_orchestrator import RegistrationOrchestrator
        
        # Test inheritance
        self.assertTrue(issubclass(ActivationOrchestrator, BaseOrchestrator))
        self.assertTrue(issubclass(RegistrationOrchestrator, BaseOrchestrator))
        
        # Test base methods are available
        activation_orchestrator = ActivationOrchestrator()
        self.assertTrue(hasattr(activation_orchestrator, '_generate_request_id'))
        self.assertTrue(hasattr(activation_orchestrator, '_handle_exceptions'))
        self.assertTrue(hasattr(activation_orchestrator, '_get_request_context'))

    def test_logging_utils_email_masking(self):
        """Test email masking functionality"""
        from user.services.logging_utils_service import LoggingUtilsService
        
        # Test email masking
        masked = LoggingUtilsService.mask_email('<EMAIL>')
        self.assertEqual(masked, 'tes***@example.com')
        
        # Test short email
        masked_short = LoggingUtilsService.mask_email('<EMAIL>')
        self.assertEqual(masked_short, '***@example.com')
        
        # Test invalid email
        masked_invalid = LoggingUtilsService.mask_email('invalid-email')
        self.assertEqual(masked_invalid, '***MASKED***')

    def test_logging_utils_device_id_masking(self):
        """Test device ID masking functionality"""
        from user.services.logging_utils_service import LoggingUtilsService
        
        # Test device ID masking
        masked = LoggingUtilsService.mask_device_id('very-long-device-id-12345678')
        self.assertEqual(masked, 'very***5678')
        
        # Test short device ID
        masked_short = LoggingUtilsService.mask_device_id('short')
        self.assertEqual(masked_short, '***MASKED***')

    def test_request_utils_data_extraction(self):
        """Test request data extraction"""
        from user.services.request_utils_service import RequestUtilsService
        
        # Test GET request
        request = self.factory.get('/test/', {'param1': 'value1', 'param2': 'value2'})
        data = RequestUtilsService.extract_request_data(request)
        self.assertEqual(data['param1'], 'value1')
        self.assertEqual(data['param2'], 'value2')

    def test_response_utils_user_data_preparation(self):
        """Test user data preparation for responses"""
        from user.services.response_utils_service import ResponseUtilsService
        
        # Test basic user data preparation
        user_data = ResponseUtilsService.prepare_user_data(self.user)
        self.assertEqual(user_data['id'], self.user.id)
        self.assertEqual(user_data['email'], self.user.email)
        self.assertEqual(user_data['name'], self.user.name)
        self.assertEqual(user_data['role'], self.user.role)
        self.assertFalse(user_data['is_mail_verified'])
        
        # Test sensitive data inclusion
        user_data_sensitive = ResponseUtilsService.prepare_user_data(self.user, include_sensitive=True)
        self.assertIn('account_address', user_data_sensitive)
        self.assertIn('is_active', user_data_sensitive)

    def test_device_management_context_constants(self):
        """Test DeviceManagementService context constants"""
        from user.services.device_management_service import DeviceManagementService
        
        # Test context constants are properly defined
        self.assertEqual(DeviceManagementService.CONTEXT_REGISTRATION, 'registration')
        self.assertEqual(DeviceManagementService.CONTEXT_ACTIVATION, 'activation')
        self.assertEqual(DeviceManagementService.CONTEXT_AUTHENTICATION, 'authentication')
        self.assertEqual(DeviceManagementService.CONTEXT_GENERAL, 'general')

    @patch('user.services.logging_utils_service.logger')
    def test_logging_utils_operation_logging(self, mock_logger):
        """Test operation logging functionality"""
        from user.services.logging_utils_service import LoggingUtilsService
        
        # Test operation step logging
        LoggingUtilsService.log_operation_step(
            operation="TEST",
            step="VALIDATION_START",
            request_id="test-123",
            user_id=self.user.id
        )
        
        # Verify logger was called
        mock_logger.info.assert_called()

    def test_modular_system_no_circular_imports(self):
        """Test that there are no circular import issues"""
        try:
            # Import all services to check for circular dependencies
            from user.services.base_orchestrator import BaseOrchestrator
            from user.services.logging_utils_service import LoggingUtilsService
            from user.services.request_utils_service import RequestUtilsService
            from user.services.response_utils_service import ResponseUtilsService
            from user.services.base_validation_service import BaseValidationService
            from user.services.device_management_service import DeviceManagementService
            
            # If we get here, no circular imports exist
            self.assertTrue(True, "No circular import issues detected")
            
        except ImportError as e:
            self.fail(f"Circular import detected: {e}")


class TestModularSystemFunctionality(TestCase):
    """Test the functionality of the modular system components"""

    def test_base_validation_service_email_validation(self):
        """Test email validation functionality"""
        from user.services.base_validation_service import BaseValidationService
        from agritram.exceptions import ValidationException
        
        # Test valid email (should not raise exception)
        try:
            BaseValidationService.validate_email_format('<EMAIL>')
        except ValidationException:
            self.fail("Valid email should not raise ValidationException")
        
        # Test invalid email (should raise exception)
        with self.assertRaises(ValidationException):
            BaseValidationService.validate_email_format('invalid-email')
        
        # Test empty email (should raise exception)
        with self.assertRaises(ValidationException):
            BaseValidationService.validate_email_format('')

    def test_base_validation_service_required_fields(self):
        """Test required fields validation"""
        from user.services.base_validation_service import BaseValidationService
        from agritram.exceptions import ValidationException
        
        # Test valid data (should not raise exception)
        valid_data = {'email': '<EMAIL>', 'name': 'Test User'}
        try:
            BaseValidationService.validate_required_fields(valid_data, ['email', 'name'])
        except ValidationException:
            self.fail("Valid data should not raise ValidationException")
        
        # Test missing fields (should raise exception)
        invalid_data = {'email': '<EMAIL>'}
        with self.assertRaises(ValidationException):
            BaseValidationService.validate_required_fields(invalid_data, ['email', 'name'])

    def test_system_integration_completeness(self):
        """Test that the modular system is complete and functional"""
        # This test verifies that all major components are properly integrated
        
        # Test that all utility services are available
        from user.services.logging_utils_service import LoggingUtilsService
        from user.services.request_utils_service import RequestUtilsService
        from user.services.response_utils_service import ResponseUtilsService
        from user.services.base_validation_service import BaseValidationService
        from user.services.device_management_service import DeviceManagementService
        
        # Test that orchestrators are available and inherit properly
        from user.services.base_orchestrator import BaseOrchestrator
        from user.services.activation_orchestrator import ActivationOrchestrator
        from user.services.registration_orchestrator import RegistrationOrchestrator
        
        # Verify inheritance
        self.assertTrue(issubclass(ActivationOrchestrator, BaseOrchestrator))
        self.assertTrue(issubclass(RegistrationOrchestrator, BaseOrchestrator))
        
        # Test that key methods are available
        orchestrator = ActivationOrchestrator()
        self.assertTrue(callable(getattr(orchestrator, 'activate_user_account', None)))
        
        registration_orchestrator = RegistrationOrchestrator()
        self.assertTrue(callable(getattr(registration_orchestrator, 'register_user', None)))
        
        self.assertTrue(True, "Modular system integration is complete")
