"""
Request Utilities Service

This service provides shared request handling functionality including:
- Request data extraction
- Header processing
- Device ID handling
- IP extraction and validation
- Parameter extraction patterns
"""

import logging
from typing import Dict, Any, Optional, Tuple
from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode
from oauth2_auth.utils import get_client_ip, get_dynamic_device_info

logger = logging.getLogger(__name__)


class RequestUtilsService:
    """Shared request utilities for user services"""

    @classmethod
    def extract_request_data(cls, request) -> Dict[str, Any]:
        """
        Extract request data from different request types
        
        Args:
            request: HTTP request object
            
        Returns:
            Dict containing request data
        """
        try:
            if hasattr(request, 'data') and request.data:
                return dict(request.data)
            elif hasattr(request, 'POST') and request.POST:
                return dict(request.POST)
            elif request.method == 'GET' and hasattr(request, 'GET'):
                return dict(request.GET)
            else:
                return {}
        except Exception as e:
            logger.warning(f"Failed to extract request data: {str(e)}")
            return {}

    @classmethod
    def get_request_context(cls, request) -> Dict[str, Any]:
        """
        Get comprehensive request context information
        
        Args:
            request: HTTP request object
            
        Returns:
            Dict containing request context
        """
        return {
            "client_ip": get_client_ip(request),
            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
            "method": request.method,
            "path": request.path,
            "content_type": request.META.get("CONTENT_TYPE", ""),
            "remote_addr": request.META.get("REMOTE_ADDR", ""),
            "http_host": request.META.get("HTTP_HOST", ""),
        }

    @classmethod
    def extract_device_id_from_request(cls, request) -> Optional[str]:
        """
        Extract device ID from multiple sources in request
        
        Args:
            request: HTTP request object
            
        Returns:
            Device ID if found, None otherwise
        """
        # Check X-Device-ID header first (highest priority)
        header_device_id = request.META.get("HTTP_X_DEVICE_ID", "").strip()
        if header_device_id:
            return header_device_id
        
        # Check request data
        request_data = cls.extract_request_data(request)
        device_id = request_data.get("device_id", "").strip()
        if device_id:
            return device_id
        
        return None

    @classmethod
    def handle_device_id_extraction(cls, request, provided_device_id: Optional[str] = None,
                                   request_id: Optional[str] = None) -> Optional[str]:
        """
        Handle device ID extraction with fallback generation
        
        Args:
            request: HTTP request object
            provided_device_id: Device ID provided in request data
            request_id: Request ID for logging
            
        Returns:
            Device ID (extracted or generated)
        """
        # Try to get from header first
        header_device_id = request.META.get("HTTP_X_DEVICE_ID", "").strip()
        if header_device_id:
            if request_id:
                logger.debug(
                    f"OPERATION_DEBUG: DEVICE_ID_FROM_HEADER | message=Device ID extracted from X-Device-ID header | request_id={request_id} | device_id={header_device_id}"
                )
            return header_device_id
        
        # Use provided device ID if available
        if provided_device_id:
            return provided_device_id
        
        # Generate new device ID if none provided
        from .device_management_service import DeviceManagementService
        generated_device_id = DeviceManagementService.generate_device_id()
        
        if request_id:
            logger.debug(
                f"OPERATION_DEBUG: DEVICE_ID_GENERATED | message=Generated device ID | request_id={request_id} | device_id={generated_device_id}"
            )
        
        return generated_device_id

    @classmethod
    def extract_activation_parameters(cls, request, uid: Optional[str] = None, 
                                    token: Optional[str] = None) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Extract activation parameters from request (GET or POST)
        
        Args:
            request: HTTP request object
            uid: UID from URL parameters
            token: Token from URL parameters
            
        Returns:
            Tuple of (uid, token, device_id)
        """
        if request.method == "GET":
            # Handle URL parameters (from email links)
            return uid, token, request.GET.get("device_id")
        else:
            # Handle POST data (from frontend forms)
            try:
                request_data = cls.extract_request_data(request)
                return (
                    request_data.get("uid") or uid,
                    request_data.get("token") or token,
                    request_data.get("device_id"),
                )
            except Exception:
                return None, None, None

    @classmethod
    def decode_uid_safely(cls, uidb64: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Safely decode base64 UID
        
        Args:
            uidb64: Base64 encoded UID
            
        Returns:
            Tuple of (success, decoded_uid, error_message)
        """
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            return True, uid, None
        except (TypeError, ValueError, OverflowError) as e:
            return False, None, f"Invalid UID format: {type(e).__name__}"

    @classmethod
    def get_device_info(cls, request, context: str = "general") -> Dict[str, Any]:
        """
        Get device information from request
        
        Args:
            request: HTTP request object
            context: Context for device info extraction
            
        Returns:
            Dict containing device information
        """
        try:
            device_info = get_dynamic_device_info(request, context)
            return {
                "device_name": device_info.get("device_name", "Unknown Device"),
                "device_type": device_info.get("device_type", "unknown"),
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "platform": device_info.get("platform", "unknown"),
            }
        except Exception as e:
            logger.warning(f"Failed to get device info: {str(e)}")
            return {
                "device_name": "Unknown Device",
                "device_type": "unknown",
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "platform": "unknown",
            }

    @classmethod
    def validate_required_parameters(cls, parameters: Dict[str, Any], 
                                   required_fields: list) -> Tuple[bool, list]:
        """
        Validate that required parameters are present
        
        Args:
            parameters: Parameters dictionary to validate
            required_fields: List of required field names
            
        Returns:
            Tuple of (is_valid, missing_fields)
        """
        missing_fields = []
        
        for field in required_fields:
            if not parameters.get(field):
                missing_fields.append(field)
        
        return len(missing_fields) == 0, missing_fields

    @classmethod
    def extract_email_from_request(cls, request) -> Optional[str]:
        """
        Extract and normalize email from request
        
        Args:
            request: HTTP request object
            
        Returns:
            Normalized email address or None
        """
        request_data = cls.extract_request_data(request)
        email = request_data.get("email", "").strip().lower()
        return email if email else None

    @classmethod
    def is_ajax_request(cls, request) -> bool:
        """
        Check if request is an AJAX request
        
        Args:
            request: HTTP request object
            
        Returns:
            True if AJAX request, False otherwise
        """
        return (
            request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest' or
            request.META.get('CONTENT_TYPE', '').startswith('application/json')
        )

    @classmethod
    def get_forwarded_ips(cls, request) -> list:
        """
        Get all forwarded IP addresses from request
        
        Args:
            request: HTTP request object
            
        Returns:
            List of IP addresses
        """
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR', '')
        if forwarded_for:
            return [ip.strip() for ip in forwarded_for.split(',')]
        return []

    @classmethod
    def get_real_ip(cls, request) -> str:
        """
        Get the real client IP address
        
        Args:
            request: HTTP request object
            
        Returns:
            Client IP address
        """
        # Check for real IP header first
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            return real_ip
        
        # Fall back to standard method
        return get_client_ip(request)
