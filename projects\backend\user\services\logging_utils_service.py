"""
Logging Utilities Service

This service provides shared logging functionality for all user services including:
- Request data masking and formatting
- Header extraction and filtering
- Single-line JSON formatting
- Standardized logging patterns
"""

import copy
import logging
from typing import Dict, Any, Optional, List
import json

logger = logging.getLogger(__name__)


class LoggingUtilsService:
    """Shared logging utilities for user services"""

    # Standard safe headers that can be logged
    SAFE_HEADERS = [
        'HTTP_USER_AGENT', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP',
        'HTTP_ACCEPT', 'HTTP_ACCEPT_LANGUAGE', 'HTTP_X_DEVICE_ID',
        'HTTP_ACCEPT_ENCODING', 'CONTENT_TYPE', 'CONTENT_LENGTH',
        'HTTP_ORIGIN', 'HTTP_REFERER', 'HTTP_HOST', 'REQUEST_METHOD',
        'PATH_INFO', 'QUERY_STRING'
    ]

    # Standard sensitive fields that should be masked
    SENSITIVE_FIELDS = ['password', 'token', 'uid', 'activation_token']

    @classmethod
    def extract_safe_headers(cls, request, additional_headers: List[str] = None) -> Dict[str, str]:
        """
        Extract safe headers from request for logging
        
        Args:
            request: HTTP request object
            additional_headers: Additional headers to include
            
        Returns:
            Dict containing safe headers
        """
        safe_headers = {}
        headers_to_extract = cls.SAFE_HEADERS.copy()
        
        if additional_headers:
            headers_to_extract.extend(additional_headers)
        
        for key in headers_to_extract:
            if hasattr(request, 'META') and key in request.META:
                safe_headers[key] = request.META[key]
        
        return safe_headers

    @classmethod
    def mask_sensitive_data(cls, data: Dict[str, Any], 
                           additional_sensitive_fields: List[str] = None) -> Dict[str, Any]:
        """
        Mask sensitive data for logging
        
        Args:
            data: Data dictionary to mask
            additional_sensitive_fields: Additional fields to mask
            
        Returns:
            Dict with sensitive data masked
        """
        if not data:
            return {}
            
        safe_data = copy.deepcopy(data)
        sensitive_fields = cls.SENSITIVE_FIELDS.copy()
        
        if additional_sensitive_fields:
            sensitive_fields.extend(additional_sensitive_fields)
        
        for field in sensitive_fields:
            if field in safe_data:
                safe_data[field] = '***MASKED***'
        
        return safe_data

    @classmethod
    def mask_email(cls, email: str) -> str:
        """
        Mask email address for logging
        
        Args:
            email: Email address to mask
            
        Returns:
            Masked email address
        """
        if not email or '@' not in email:
            return '***MASKED***'
        
        local, domain = email.split('@', 1)
        if len(local) <= 3:
            return f"***@{domain}"
        return f"{local[:3]}***@{domain}"

    @classmethod
    def mask_device_id(cls, device_id: str) -> str:
        """
        Mask device ID for logging
        
        Args:
            device_id: Device ID to mask
            
        Returns:
            Masked device ID
        """
        if not device_id or len(device_id) <= 8:
            return '***MASKED***'
        return f"{device_id[:4]}***{device_id[-4:]}"

    @classmethod
    def mask_headers(cls, headers: Dict[str, str]) -> Dict[str, str]:
        """
        Mask sensitive headers for logging
        
        Args:
            headers: Headers dictionary to mask
            
        Returns:
            Dict with sensitive headers masked
        """
        safe_headers = copy.deepcopy(headers)
        
        # Mask device ID in headers
        if 'HTTP_X_DEVICE_ID' in safe_headers:
            safe_headers['HTTP_X_DEVICE_ID'] = cls.mask_device_id(safe_headers['HTTP_X_DEVICE_ID'])
        
        # Mask any authorization headers that might have slipped through
        auth_headers = ['HTTP_AUTHORIZATION', 'HTTP_COOKIE', 'HTTP_X_API_KEY']
        for auth_header in auth_headers:
            if auth_header in safe_headers:
                safe_headers[auth_header] = '***MASKED***'
        
        return safe_headers

    @classmethod
    def create_single_line_json(cls, request_data: Dict[str, Any], headers: Dict[str, str],
                               email: Optional[str] = None, 
                               additional_sensitive_fields: List[str] = None) -> str:
        """
        Create single-line JSON log data with proper masking
        
        Args:
            request_data: Request data to log
            headers: Request headers to log
            email: Email to mask (optional)
            additional_sensitive_fields: Additional sensitive fields to mask
            
        Returns:
            JSON string for logging
        """
        # Mask sensitive data
        safe_data = cls.mask_sensitive_data(request_data, additional_sensitive_fields)
        
        # Mask email if provided
        if email:
            safe_data['email'] = cls.mask_email(email)

        # Mask sensitive headers
        safe_headers = cls.mask_headers(headers)

        # Combine data and headers
        log_data = {"request_data": safe_data, "headers": safe_headers}

        # Return as single-line JSON
        try:
            return json.dumps(log_data, separators=(",", ":"))
        except Exception as e:
            # Fallback if JSON serialization fails
            return f'{{"error": "JSON serialization failed: {str(e)}"}}'

    @classmethod
    def log_operation_start(cls, operation: str, request, request_id: str,
                           additional_data: Dict[str, Any] = None) -> None:
        """
        Log the start of an operation with standardized format
        
        Args:
            operation: Operation name (e.g., "REGISTRATION", "ACTIVATION")
            request: HTTP request object
            request_id: Unique request ID
            additional_data: Additional data to include in logs
        """
        from oauth2_auth.utils import get_client_ip
        
        client_ip = get_client_ip(request)
        
        try:
            # Extract request data safely
            request_data = cls._extract_request_data_safely(request)
            headers = cls.extract_safe_headers(request)
            
            # Add any additional data to request_data for logging
            if additional_data:
                request_data.update(additional_data)

            log_data = cls.create_single_line_json(request_data, headers)

            logger.info(
                f"OPERATION_INFO: {operation}_REQUEST_START | message={operation.title()} request received | request_id={request_id} | method={request.method} | ip={client_ip} | data={log_data}"
            )
        except Exception as e:
            # Fallback logging if data extraction fails
            logger.info(
                f"OPERATION_INFO: {operation}_REQUEST_START | message={operation.title()} request received | request_id={request_id} | method={request.method} | ip={client_ip} | data_extraction_error={str(e)}"
            )

    @classmethod
    def log_operation_step(cls, operation: str, step: str, request_id: str,
                          user_id: Optional[int] = None, **kwargs) -> None:
        """
        Log an operation step with standardized format
        
        Args:
            operation: Operation name
            step: Step name
            request_id: Request ID
            user_id: User ID (optional)
            **kwargs: Additional metadata
        """
        extra_data = {
            "operation": f"{operation}_{step}",
            "request_id": request_id,
        }
        
        if user_id:
            extra_data["user_id"] = user_id
            
        extra_data.update(kwargs)

        logger.info(
            f"{operation} {step.replace('_', ' ').title()}",
            extra=extra_data,
        )

    @classmethod
    def log_user_event(cls, event_type: str, user, request_id: str = None, **kwargs) -> None:
        """
        Log a user-related event with standardized format
        
        Args:
            event_type: Type of event (e.g., "USER_CREATED", "USER_ACTIVATED")
            user: User instance
            request_id: Request ID (optional)
            **kwargs: Additional metadata
        """
        extra_data = {
            "operation": event_type,
            "user_id": user.id,
            "email": cls.mask_email(user.email),
        }
        
        if request_id:
            extra_data["request_id"] = request_id
            
        extra_data.update(kwargs)

        logger.info(
            f"User event: {event_type.replace('_', ' ').title()}",
            extra=extra_data,
        )

    @classmethod
    def _extract_request_data_safely(cls, request) -> Dict[str, Any]:
        """
        Safely extract request data for logging
        
        Args:
            request: HTTP request object
            
        Returns:
            Dict containing request data
        """
        try:
            if hasattr(request, 'data') and request.data:
                return dict(request.data)
            elif hasattr(request, 'POST') and request.POST:
                return dict(request.POST)
            elif request.method == 'GET' and hasattr(request, 'GET'):
                return dict(request.GET)
            else:
                return {}
        except Exception:
            return {}
