"""
Device Management Service

This service handles all device-related functionality for user operations including:
- Device ID generation and validation
- Device information extraction and processing
- Device registration and tracking
- Cross-flow device validation
- Generic device operations for registration, activation, and authentication
"""

from typing import Dict, Any, Tuple, Optional
from django.utils import timezone
from oauth2_auth.utils import (
    get_client_ip,
    generate_device_fingerprint,
    get_dynamic_device_info,
)
from oauth2_auth.device_validation_service import device_validation_service
from oauth2_auth.authentication import DeviceAuthenticationService
from .logging_utils_service import LoggingUtilsService
import logging

logger = logging.getLogger(__name__)


class DeviceManagementService:
    """Generic service for handling all device-related operations across user flows"""

    # Supported operation contexts
    CONTEXT_REGISTRATION = "registration"
    CONTEXT_ACTIVATION = "activation"
    CONTEXT_AUTHENTICATION = "authentication"
    CONTEXT_GENERAL = "general"

    @staticmethod
    def generate_device_id() -> str:
        """
        Generate a cryptographically secure device ID

        Returns:
            str: Generated device ID
        """
        import secrets

        return f"{timezone.now().strftime('%Y%m%d%H%M%S')}_{secrets.token_urlsafe(32)}"

    @staticmethod
    def extract_device_info(request, context: str = "registration") -> Dict[str, Any]:
        """
        Extract comprehensive device information from request

        Args:
            request: HTTP request object
            context: Context for device info extraction

        Returns:
            Dict containing device information
        """
        device_info = get_dynamic_device_info(request, context)
        client_ip = get_client_ip(request)
        fingerprint = generate_device_fingerprint(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        return {
            "device_name": device_info["device_name"],
            "device_type": device_info["device_type"],
            "client_ip": client_ip,
            "fingerprint": fingerprint,
            "user_agent": user_agent,
            "full_device_info": device_info,
        }

    @classmethod
    def register_device(
        cls,
        user,
        device_id: str,
        device_name: str,
        device_type: str,
        request,
        context: str = None,
        request_id: Optional[str] = None,
    ) -> Tuple[bool, Optional[str]]:
        """
        Register a device for the user with enhanced logging and context awareness

        Args:
            user: User instance
            device_id: Device identifier
            device_name: Device name
            device_type: Device type
            request: HTTP request object
            context: Operation context (registration, activation, etc.)
            request_id: Request ID for correlation

        Returns:
            Tuple of (success: bool, error_message: Optional[str])
        """
        if context is None:
            context = cls.CONTEXT_GENERAL

        try:
            # Log device registration start
            LoggingUtilsService.log_operation_step(
                operation=context.upper(),
                step="DEVICE_REGISTRATION_START",
                request_id=request_id,
                user_id=user.id,
                device_id=device_id,
                device_name=device_name,
                device_type=device_type,
            )

            # Register device using authentication service
            DeviceAuthenticationService.register_device(
                user, device_id, device_name, device_type, request
            )

            # Log successful registration
            LoggingUtilsService.log_operation_step(
                operation=context.upper(),
                step="DEVICE_REGISTRATION_SUCCESS",
                request_id=request_id,
                user_id=user.id,
                device_id=device_id,
                device_name=device_name,
                device_type=device_type,
            )

            return True, None

        except Exception as e:
            # Log registration failure
            LoggingUtilsService.log_operation_step(
                operation=context.upper(),
                step="DEVICE_REGISTRATION_FAILED",
                request_id=request_id,
                user_id=user.id,
                device_id=device_id,
                error=str(e),
                error_type=type(e).__name__,
            )
            return False, str(e)

    @classmethod
    def track_device(
        cls,
        user,
        device_id: str,
        request,
        context: str = None,
        request_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Track device for cross-flow validation with context awareness

        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            context: Operation context (registration, activation, etc.)
            request_id: Request ID for correlation

        Returns:
            Dict containing tracking information
        """
        if context is None:
            context = cls.CONTEXT_GENERAL

        try:
            # Log device tracking start
            LoggingUtilsService.log_operation_step(
                operation=context.upper(),
                step="DEVICE_TRACKING_START",
                request_id=request_id,
                user_id=user.id,
                device_id=device_id,
            )

            # Track device using validation service
            if context == cls.CONTEXT_REGISTRATION:
                tracking_info = device_validation_service.track_registration_device(
                    user=user, device_id=device_id, request=request
                )
            else:
                # For other contexts, use generic tracking
                tracking_info = cls._track_generic_device(
                    user, device_id, request, context
                )

            # Log successful tracking
            LoggingUtilsService.log_operation_step(
                operation=context.upper(),
                step="DEVICE_TRACKING_SUCCESS",
                request_id=request_id,
                user_id=user.id,
                device_id=device_id,
                tracking_successful=True,
            )

            return tracking_info

        except Exception as e:
            # Log tracking failure
            LoggingUtilsService.log_operation_step(
                operation=context.upper(),
                step="DEVICE_TRACKING_FAILED",
                request_id=request_id,
                user_id=user.id,
                device_id=device_id,
                error=str(e),
                error_type=type(e).__name__,
            )
            return {"tracking_successful": False, "error": str(e)}

    @classmethod
    def track_registration_device(cls, user, device_id: str, request) -> Dict[str, Any]:
        """
        Legacy method for backward compatibility - delegates to track_device
        """
        return cls.track_device(user, device_id, request, cls.CONTEXT_REGISTRATION)

    @staticmethod
    def _track_generic_device(
        user, device_id: str, request, context: str
    ) -> Dict[str, Any]:
        """
        Generic device tracking for non-registration contexts
        """
        device_info = DeviceManagementService.extract_device_info(request, context)
        return {
            "tracking_successful": True,
            "device_id": device_id,
            "context": context,
            "device_info": device_info,
            "timestamp": timezone.now().isoformat(),
        }

    @classmethod
    def handle_device_flow(
        cls,
        user,
        device_data: Dict[str, Any],
        request,
        context: str = None,
        request_id: Optional[str] = None,
        track_device: bool = True,
    ) -> Dict[str, Any]:
        """
        Handle the complete device flow for any context (registration, activation, etc.)

        Args:
            user: User instance
            device_data: Device information dictionary
            request: HTTP request object
            context: Operation context (registration, activation, etc.)
            request_id: Request ID for correlation
            track_device: Whether to track the device for cross-flow validation

        Returns:
            Dict containing device flow results
        """
        if context is None:
            context = cls.CONTEXT_GENERAL

        device_id = device_data["device_id"]
        device_name = device_data["device_name"]
        device_type = device_data["device_type"]

        # Register the device
        device_registered, registration_error = cls.register_device(
            user, device_id, device_name, device_type, request, context, request_id
        )

        # Track device for cross-flow validation if requested
        tracking_info = {}
        if track_device:
            tracking_info = cls.track_device(
                user, device_id, request, context, request_id
            )

        return {
            "device_registered": device_registered,
            "registration_error": registration_error,
            "tracking_info": tracking_info,
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
            "context": context,
        }

    @classmethod
    def handle_device_registration_flow(
        cls, user, device_data: Dict[str, Any], request
    ) -> Dict[str, Any]:
        """
        Legacy method for backward compatibility - delegates to handle_device_flow
        """
        return cls.handle_device_flow(
            user, device_data, request, cls.CONTEXT_REGISTRATION
        )

    @classmethod
    def handle_activation_device_flow(
        cls,
        user,
        device_id: str,
        device_name: str,
        device_type: str,
        request,
        request_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Handle device flow specifically for activation context
        """
        device_data = {
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
        }
        return cls.handle_device_flow(
            user,
            device_data,
            request,
            cls.CONTEXT_ACTIVATION,
            request_id,
            track_device=False,
        )
